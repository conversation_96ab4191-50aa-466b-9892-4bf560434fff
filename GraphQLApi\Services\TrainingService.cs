using GraphQLApi.Data;
using Microsoft.EntityFrameworkCore;
using Shared.GraphQL.Models;
using HotChocolate;

namespace GraphQLApi.Services;

public class TrainingService : ITrainingService
{
    private readonly IDbContextFactory<AppDbContext> _contextFactory;
    private readonly ILogger<TrainingService> _logger;

    public TrainingService(
        IDbContextFactory<AppDbContext> contextFactory,
        ILogger<TrainingService> logger)
    {
        _contextFactory = contextFactory;
        _logger = logger;
    }

    public async Task<IEnumerable<Training>> GetAllTrainingsAsync()
    {
        await using var context = await _contextFactory.CreateDbContextAsync();
        return await context.Trainings
            .Include(t => t.Workers)
            .ToListAsync();
    }

    public async Task<Training?> GetTrainingByIdAsync(int id)
    {
        await using var context = await _contextFactory.CreateDbContextAsync();
        return await context.Trainings
            .Include(t => t.Workers)
            .FirstOrDefaultAsync(t => t.Id == id);
    }

    public async Task<Training> CreateTrainingAsync(Training training)
    {
        await using var context = await _contextFactory.CreateDbContextAsync();

        // Check if training with same name already exists
        var existingTraining = await context.Trainings
            .FirstOrDefaultAsync(t => t.Name.ToLower() == training.Name.ToLower());

        if (existingTraining != null)
        {
            throw new GraphQLException(new Error(
                "Validation",
                $"A training with name '{training.Name}' already exists.")
            );
        }

        context.Trainings.Add(training);
        await context.SaveChangesAsync();
        return training;
    }

    public async Task<Training?> UpdateTrainingAsync(int id, Training training)
    {
        await using var context = await _contextFactory.CreateDbContextAsync();
        var existingTraining = await context.Trainings
            .Include(t => t.Workers)
            .FirstOrDefaultAsync(t => t.Id == id);

        if (existingTraining == null)
        {
            return null;
        }

        // Check if another training with the same name exists
        var duplicateTraining = await context.Trainings
            .FirstOrDefaultAsync(t => t.Id != id && t.Name.ToLower() == training.Name.ToLower());

        if (duplicateTraining != null)
        {
            throw new GraphQLException(new Error(
                "Validation",
                $"A training with name '{training.Name}' already exists.")
            );
        }

        // Update properties
        existingTraining.Name = training.Name;
        existingTraining.Description = training.Description;
        existingTraining.StartDate = training.StartDate;
        existingTraining.Duration = training.Duration;
        existingTraining.TrainingType = training.TrainingType;
        existingTraining.Trainer = training.Trainer;
        existingTraining.Frequency = training.Frequency;
        existingTraining.Status = training.Status;

        await context.SaveChangesAsync();
        return existingTraining;
    }

    public async Task<bool> DeleteTrainingAsync(int id)
    {
        await using var context = await _contextFactory.CreateDbContextAsync();
        var training = await context.Trainings.FindAsync(id);

        if (training == null)
        {
            return false;
        }

        try
        {
            context.Trainings.Remove(training);
            await context.SaveChangesAsync();
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting training with ID {TrainingId}", id);
            return false;
        }
    }
}
