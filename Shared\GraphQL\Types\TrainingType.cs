using HotChocolate.Types;
using Shared.GraphQL.Models;
using Shared.Enums;

namespace Shared.GraphQL.Types
{
    public class TrainingType : ObjectType<Training>
    {
        protected override void Configure(IObjectTypeDescriptor<Training> descriptor)
        {
            descriptor.Field(t => t.Id).Type<NonNullType<IntType>>();
            descriptor.Field(t => t.Name).Type<NonNullType<StringType>>();
            descriptor.Field(t => t.Description).Type<StringType>();
            descriptor.Field(t => t.Workers).Type<ListType<WorkerType>>();
            descriptor.Field(t => t.Duration).Type<StringType>();
            descriptor.Field(t => t.ParsedDuration).Type<StringType>()
                .Resolve(context =>
                {
                    var training = context.Parent<Training>();
                    return training.ParsedDuration?.ToString();
                });
            descriptor.Field(t => t.StartDate).Type<DateTimeType>();
            descriptor.Field(t => t.TrainingType).Type<StringType>();
            descriptor.Field(t => t.Trainer).Type<StringType>();
            descriptor.Field(t => t.Frequency).Type<StringType>();
            descriptor.Field(t => t.Status).Type<NonNullType<EnumType<TrainingStatus>>>();
            descriptor.Field(t => t.CreatedAt).Type<NonNullType<DateTimeType>>();
            descriptor.Field(t => t.CreatedBy).Type<NonNullType<StringType>>();
            descriptor.Field(t => t.UpdatedAt).Type<DateTimeType>();
            descriptor.Field(t => t.UpdatedBy).Type<NonNullType<StringType>>();
        }
    }
}
